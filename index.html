<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Spreadtree Infra and Tech</title>
</head>
<body style="margin:0; font-family: Arial, sans-serif;">

  <!-- NAVBAR -->
  <nav style="background-color:#000; padding:0 20px; font-family: Arial, sans-serif;">
    <div style="max-width:1200px; margin: 0 auto; display: flex; align-items: center; justify-content: space-between; height: 60px;">
      <!-- Logo / Brand -->
      <a href="#home" style="color:white; font-size: 24px; font-weight: bold; text-decoration: none;">Spreadtree</a>

      <!-- Navbar Links -->
      <ul style="list-style:none; margin: 0; padding: 0; display: flex;">
        <li style="position: relative;">
          <a href="About US.html" style="display:block; padding: 0 15px; line-height: 60px; color: white; text-decoration: none;">About Us</a>
        </li>
        <li style="position: relative;">
          <a href="#vision" style="display:block; padding: 0 15px; line-height: 60px; color: white; text-decoration: none;">Vision and Mission</a>
        </li>
        <li style="position: relative;">
          <a href="#solutions" style="display:block; padding: 0 15px; line-height: 60px; color: white; text-decoration: none; cursor: pointer;">Services ▼</a>
          <!-- Dropdown -->
          <ul style="display: none; position: absolute; top: 60px; left: 0; background: #222; padding: 0; margin: 0; list-style: none; min-width: 200px; z-index: 1000;">
            <li><a href="#ftth" style="color: white; padding: 12px 20px; display: block; text-decoration: none;">FTTH Deployment</a></li>
            <li><a href="#lastmile" style="color: white; padding: 12px 20px; display: block; text-decoration: none;">Last Mile Connectivity</a></li>
            <li><a href="#nld" style="color: white; padding: 12px 20px; display: block; text-decoration: none;">NLD Fiber Projects</a></li>
            <li><a href="#enterprise" style="color: white; padding: 12px 20px; display: block; text-decoration: none;">Enterprise Networking</a></li>
          </ul>
        </li>
        <li style="position: relative;">
          <a href="#milestone" style="display:block; padding: 0 15px; line-height: 60px; color: white; text-decoration: none;">Company Milestone</a>
        </li>
        <li style="position: relative;">
          <a href="#contact" style="display:block; padding: 0 15px; line-height: 60px; color: white; text-decoration: none;">Contact Us</a>
        </li>
      </ul>
    </div>
  </nav>

  <!-- JS for Dropdown Hover -->
  <script>
    // Dropdown hover toggle
    document.addEventListener('DOMContentLoaded', function() {
      const solutionsMenu = document.querySelector('nav ul li:nth-child(3)');
      const dropdown = solutionsMenu.querySelector('ul');

      solutionsMenu.addEventListener('mouseenter', () => {
        dropdown.style.display = 'block';
      });
      solutionsMenu.addEventListener('mouseleave', () => {
        dropdown.style.display = 'none';
      });
    });
  </script>

  <!-- CONTENT AREA - Ready for your content -->
  <main>
    <!-- Your home page content will go here -->
  </main>

</body>
</html>
